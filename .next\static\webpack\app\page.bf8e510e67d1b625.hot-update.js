"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ui/sticky-scroll.tsx":
/*!*****************************************!*\
  !*** ./components/ui/sticky-scroll.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var lenis_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lenis/react */ \"(app-pages-browser)/./node_modules/lenis/dist/lenis-react.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst StickyScrollComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (props, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(lenis_react__WEBPACK_IMPORTED_MODULE_2__.ReactLenis, {\n        root: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"bg-black\",\n            ref: ref,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"text-white h-[80vh] w-full bg-black grid place-content-center sticky top-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:54px_54px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-4 px-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium tracking-[0.2em] text-gold/60 uppercase block\",\n                                        children: \"Heritage\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"2xl:text-6xl xl:text-5xl text-4xl font-serif font-light tracking-tight leading-[110%] text-gold\",\n                                        children: [\n                                            \"Our Story\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-3xl md:text-4xl xl:text-4xl text-ivory/90 font-light italic mt-1\",\n                                                children: \"Through Time\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-px bg-gradient-to-r from-transparent via-gold to-transparent w-16 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base md:text-lg text-ivory/70 font-light tracking-wide max-w-xl mx-auto leading-relaxed\",\n                                        children: \"Discover the journey of JOOKA through our visual story of craftsmanship, elegance, and timeless design\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-ivory/50 font-light tracking-wide block pt-2\",\n                                        children: \"Scroll down to explore \\uD83D\\uDC47\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"text-white w-full bg-black\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-3 p-4 max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-3 col-span-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500&auto=format&fit=crop\",\n                                            alt: \"Luxury fashion atelier\",\n                                            className: \"transition-all duration-500 w-full h-80 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=500&auto=format&fit=crop\",\n                                            alt: \"Elegant fashion design\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=500&auto=format&fit=crop\",\n                                            alt: \"Sustainable fashion materials\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=500&auto=format&fit=crop\",\n                                            alt: \"Artisan craftsmanship\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=500&auto=format&fit=crop\",\n                                            alt: \"Fashion heritage\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-0 h-screen w-full col-span-4 gap-3 grid grid-rows-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full h-full relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1445205170230-053b83016050?w=500&auto=format&fit=crop\",\n                                                alt: \"JOOKA heritage collection\",\n                                                className: \"transition-all duration-500 h-full w-full align-bottom object-cover rounded-md group-hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-6 left-6 right-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-serif font-light text-gold mb-2\",\n                                                        children: \"2019\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-ivory/80 font-light\",\n                                                        children: \"The beginning of our journey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full h-full relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1490481651871-ab68de25d43d?w=500&auto=format&fit=crop\",\n                                                alt: \"Timeless elegance\",\n                                                className: \"transition-all duration-500 h-full w-full align-bottom object-cover rounded-md group-hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-6 left-6 right-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-serif font-light text-gold mb-2\",\n                                                        children: \"Craftsmanship\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-ivory/80 font-light\",\n                                                        children: \"Meticulous attention to detail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full h-full relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1483985988355-763728e1935b?w=500&auto=format&fit=crop\",\n                                                alt: \"Modern luxury fashion\",\n                                                className: \"transition-all duration-500 h-full w-full align-bottom object-cover rounded-md group-hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-6 left-6 right-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-serif font-light text-gold mb-2\",\n                                                        children: \"Innovation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-ivory/80 font-light\",\n                                                        children: \"Modern luxury redefined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-3 col-span-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1496747611176-843222e1e57c?w=500&auto=format&fit=crop\",\n                                            alt: \"Fashion innovation\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?w=500&auto=format&fit=crop\",\n                                            alt: \"Luxury retail experience\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&auto=format&fit=crop\",\n                                            alt: \"Sustainable luxury\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1509631179647-0177331693ae?w=500&auto=format&fit=crop\",\n                                            alt: \"Fashion craftsmanship\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"figure\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500&auto=format&fit=crop\",\n                                            alt: \"JOOKA legacy\",\n                                            className: \"transition-all duration-500 w-full h-96 align-bottom object-cover rounded-md hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    className: \"group bg-black py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-px bg-gradient-to-r from-transparent via-gold to-transparent w-32 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-[12vw] md:text-[8vw] lg:text-[6vw] leading-[100%] uppercase font-serif font-light bg-gradient-to-r from-gold via-gold/80 to-ivory bg-clip-text text-transparent transition-all ease-linear\",\n                                    children: \"JOOKA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-ivory/60 font-light tracking-wide max-w-2xl mx-auto\",\n                                    children: \"Where timeless elegance meets modern craftsmanship. Every piece tells a story of heritage, sustainability, and uncompromising quality.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-px bg-gradient-to-r from-transparent via-gold to-transparent w-32 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\sticky-scroll.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = StickyScrollComponent;\nStickyScrollComponent.displayName = \"StickyScrollComponent\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (StickyScrollComponent);\nvar _c, _c1;\n$RefreshReg$(_c, \"StickyScrollComponent$forwardRef\");\n$RefreshReg$(_c1, \"StickyScrollComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvc3RpY2t5LXNjcm9sbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQ3lDO0FBQ0M7QUFFMUMsTUFBTUcsc0NBQXdCRCxpREFBVUEsTUFBYyxDQUFDRSxPQUFPQztJQUM1RCxxQkFDRSw4REFBQ0wsbURBQVVBO1FBQUNNLElBQUk7a0JBQ2QsNEVBQUNDO1lBQUtDLFdBQVU7WUFBV0gsS0FBS0E7OzhCQUM5Qiw4REFBQ0k7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFRRixXQUFVOzswQ0FDakIsOERBQUNDO2dDQUFJRCxXQUFVOzs7Ozs7MENBRWYsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0c7d0NBQUtILFdBQVU7a0RBQW9FOzs7Ozs7a0RBR3BGLDhEQUFDSTt3Q0FBR0osV0FBVTs7NENBQWtHOzBEQUU5Ryw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQThFOzs7Ozs7Ozs7Ozs7a0RBSWhHLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDSzt3Q0FBRUwsV0FBVTtrREFBK0Y7Ozs7OztrREFHNUcsOERBQUNHO3dDQUFLSCxXQUFVO2tEQUE0RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2xGLDhEQUFDRTtvQkFBUUYsV0FBVTs4QkFDakIsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDTTt3Q0FBT04sV0FBVTtrREFDaEIsNEVBQUNPOzRDQUNDQyxLQUFJOzRDQUNKQyxLQUFJOzRDQUNKVCxXQUFVOzs7Ozs7Ozs7OztrREFHZCw4REFBQ007d0NBQU9OLFdBQVU7a0RBQ2hCLDRFQUFDTzs0Q0FDQ0MsS0FBSTs0Q0FDSkMsS0FBSTs0Q0FDSlQsV0FBVTs7Ozs7Ozs7Ozs7a0RBR2QsOERBQUNNO3dDQUFPTixXQUFVO2tEQUNoQiw0RUFBQ087NENBQ0NDLEtBQUk7NENBQ0pDLEtBQUk7NENBQ0pULFdBQVU7Ozs7Ozs7Ozs7O2tEQUdkLDhEQUFDTTt3Q0FBT04sV0FBVTtrREFDaEIsNEVBQUNPOzRDQUNDQyxLQUFJOzRDQUNKQyxLQUFJOzRDQUNKVCxXQUFVOzs7Ozs7Ozs7OztrREFHZCw4REFBQ007d0NBQU9OLFdBQVU7a0RBQ2hCLDRFQUFDTzs0Q0FDQ0MsS0FBSTs0Q0FDSkMsS0FBSTs0Q0FDSlQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSWhCLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNNO3dDQUFPTixXQUFVOzswREFDaEIsOERBQUNPO2dEQUNDQyxLQUFJO2dEQUNKQyxLQUFJO2dEQUNKVCxXQUFVOzs7Ozs7MERBRVosOERBQUNDO2dEQUFJRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ1U7d0RBQUdWLFdBQVU7a0VBQStDOzs7Ozs7a0VBQzdELDhEQUFDSzt3REFBRUwsV0FBVTtrRUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEQsOERBQUNNO3dDQUFPTixXQUFVOzswREFDaEIsOERBQUNPO2dEQUNDQyxLQUFJO2dEQUNKQyxLQUFJO2dEQUNKVCxXQUFVOzs7Ozs7MERBRVosOERBQUNDO2dEQUFJRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ1U7d0RBQUdWLFdBQVU7a0VBQStDOzs7Ozs7a0VBQzdELDhEQUFDSzt3REFBRUwsV0FBVTtrRUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEQsOERBQUNNO3dDQUFPTixXQUFVOzswREFDaEIsOERBQUNPO2dEQUNDQyxLQUFJO2dEQUNKQyxLQUFJO2dEQUNKVCxXQUFVOzs7Ozs7MERBRVosOERBQUNDO2dEQUFJRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ1U7d0RBQUdWLFdBQVU7a0VBQStDOzs7Ozs7a0VBQzdELDhEQUFDSzt3REFBRUwsV0FBVTtrRUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEQsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ007d0NBQU9OLFdBQVU7a0RBQ2hCLDRFQUFDTzs0Q0FDQ0MsS0FBSTs0Q0FDSkMsS0FBSTs0Q0FDSlQsV0FBVTs7Ozs7Ozs7Ozs7a0RBR2QsOERBQUNNO3dDQUFPTixXQUFVO2tEQUNoQiw0RUFBQ087NENBQ0NDLEtBQUk7NENBQ0pDLEtBQUk7NENBQ0pULFdBQVU7Ozs7Ozs7Ozs7O2tEQUdkLDhEQUFDTTt3Q0FBT04sV0FBVTtrREFDaEIsNEVBQUNPOzRDQUNDQyxLQUFJOzRDQUNKQyxLQUFJOzRDQUNKVCxXQUFVOzs7Ozs7Ozs7OztrREFHZCw4REFBQ007d0NBQU9OLFdBQVU7a0RBQ2hCLDRFQUFDTzs0Q0FDQ0MsS0FBSTs0Q0FDSkMsS0FBSTs0Q0FDSlQsV0FBVTs7Ozs7Ozs7Ozs7a0RBR2QsOERBQUNNO3dDQUFPTixXQUFVO2tEQUNoQiw0RUFBQ087NENBQ0NDLEtBQUk7NENBQ0pDLEtBQUk7NENBQ0pULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3BCLDhEQUFDVztvQkFBT1gsV0FBVTs4QkFDaEIsNEVBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNJO29DQUFHSixXQUFVOzhDQUFrTTs7Ozs7OzhDQUdoTiw4REFBQ0s7b0NBQUVMLFdBQVU7OENBQTJEOzs7Ozs7OENBR3hFLDhEQUFDQztvQ0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzdCOztBQUVBTCxzQkFBc0JpQixXQUFXLEdBQUc7QUFFcEMsK0RBQWVqQixxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy91aS9zdGlja3ktc2Nyb2xsLnRzeD82NmQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IFJlYWN0TGVuaXMgfSBmcm9tICdsZW5pcy9yZWFjdCc7XG5pbXBvcnQgUmVhY3QsIHsgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcblxuY29uc3QgU3RpY2t5U2Nyb2xsQ29tcG9uZW50ID0gZm9yd2FyZFJlZjxIVE1MRWxlbWVudD4oKHByb3BzLCByZWYpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8UmVhY3RMZW5pcyByb290PlxuICAgICAgPG1haW4gY2xhc3NOYW1lPSdiZy1ibGFjaycgcmVmPXtyZWZ9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nd3JhcHBlcic+XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIGgtWzgwdmhdIHctZnVsbCBiZy1ibGFjayBncmlkIHBsYWNlLWNvbnRlbnQtY2VudGVyIHN0aWNreSB0b3AtMCc+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYWJzb2x1dGUgYm90dG9tLTAgbGVmdC0wIHJpZ2h0LTAgdG9wLTAgYmctW2xpbmVhci1ncmFkaWVudCh0b19yaWdodCwjNGY0ZjRmMmVfMXB4LHRyYW5zcGFyZW50XzFweCksbGluZWFyLWdyYWRpZW50KHRvX2JvdHRvbSwjNGY0ZjRmMmVfMXB4LHRyYW5zcGFyZW50XzFweCldIGJnLVtzaXplOjU0cHhfNTRweF0gW21hc2staW1hZ2U6cmFkaWFsLWdyYWRpZW50KGVsbGlwc2VfNjAlXzUwJV9hdF81MCVfMCUsIzAwMF83MCUsdHJhbnNwYXJlbnRfMTAwJSldJz48L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS15LTQgcHgtNlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYWNraW5nLVswLjJlbV0gdGV4dC1nb2xkLzYwIHVwcGVyY2FzZSBibG9ja1wiPlxuICAgICAgICAgICAgICAgIEhlcml0YWdlXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT0nMnhsOnRleHQtNnhsIHhsOnRleHQtNXhsIHRleHQtNHhsIGZvbnQtc2VyaWYgZm9udC1saWdodCB0cmFja2luZy10aWdodCBsZWFkaW5nLVsxMTAlXSB0ZXh0LWdvbGQnPlxuICAgICAgICAgICAgICAgIE91ciBTdG9yeVxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtM3hsIG1kOnRleHQtNHhsIHhsOnRleHQtNHhsIHRleHQtaXZvcnkvOTAgZm9udC1saWdodCBpdGFsaWMgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgVGhyb3VnaCBUaW1lXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtcHggYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS1nb2xkIHRvLXRyYW5zcGFyZW50IHctMTYgbXgtYXV0b1wiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmFzZSBtZDp0ZXh0LWxnIHRleHQtaXZvcnkvNzAgZm9udC1saWdodCB0cmFja2luZy13aWRlIG1heC13LXhsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgRGlzY292ZXIgdGhlIGpvdXJuZXkgb2YgSk9PS0EgdGhyb3VnaCBvdXIgdmlzdWFsIHN0b3J5IG9mIGNyYWZ0c21hbnNoaXAsIGVsZWdhbmNlLCBhbmQgdGltZWxlc3MgZGVzaWduXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWl2b3J5LzUwIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSBibG9jayBwdC0yXCI+XG4gICAgICAgICAgICAgICAgU2Nyb2xsIGRvd24gdG8gZXhwbG9yZSDwn5GHXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHctZnVsbCBiZy1ibGFjayc+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2dyaWQgZ3JpZC1jb2xzLTEyIGdhcC0zIHAtNCBtYXgtdy03eGwgbXgtYXV0byc+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZ3JpZCBnYXAtMyBjb2wtc3Bhbi00Jz5cbiAgICAgICAgICAgICAgPGZpZ3VyZSBjbGFzc05hbWU9J3ctZnVsbCc+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPSdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ0MTk4NjMwMDkxNy02NDY3NGJkNjAwZDg/dz01MDAmYXV0bz1mb3JtYXQmZml0PWNyb3AnXG4gICAgICAgICAgICAgICAgICBhbHQ9J0x1eHVyeSBmYXNoaW9uIGF0ZWxpZXInXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J3RyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCB3LWZ1bGwgaC04MCBhbGlnbi1ib3R0b20gb2JqZWN0LWNvdmVyIHJvdW5kZWQtbWQgaG92ZXI6c2NhbGUtMTA1J1xuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZmlndXJlPlxuICAgICAgICAgICAgICA8ZmlndXJlIGNsYXNzTmFtZT0ndy1mdWxsJz5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9J2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTU4NzY5MTMyLWNiMWFlYTQ1OGM1ZT93PTUwMCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCdcbiAgICAgICAgICAgICAgICAgIGFsdD0nRWxlZ2FudCBmYXNoaW9uIGRlc2lnbidcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHctZnVsbCBoLTk2IGFsaWduLWJvdHRvbSBvYmplY3QtY292ZXIgcm91bmRlZC1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICAgIDxmaWd1cmUgY2xhc3NOYW1lPSd3LWZ1bGwnPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz0naHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MTU4ODY2NTc2MTMtOWYzNTE1YjBjNzhmP3c9NTAwJmF1dG89Zm9ybWF0JmZpdD1jcm9wJ1xuICAgICAgICAgICAgICAgICAgYWx0PSdTdXN0YWluYWJsZSBmYXNoaW9uIG1hdGVyaWFscydcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHctZnVsbCBoLTk2IGFsaWduLWJvdHRvbSBvYmplY3QtY292ZXIgcm91bmRlZC1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICAgIDxmaWd1cmUgY2xhc3NOYW1lPSd3LWZ1bGwnPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz0naHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0NjkzMzQwMzEyMTgtZTM4MmE3MWI3MTZiP3c9NTAwJmF1dG89Zm9ybWF0JmZpdD1jcm9wJ1xuICAgICAgICAgICAgICAgICAgYWx0PSdBcnRpc2FuIGNyYWZ0c21hbnNoaXAnXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J3RyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCB3LWZ1bGwgaC05NiBhbGlnbi1ib3R0b20gb2JqZWN0LWNvdmVyIHJvdW5kZWQtbWQgaG92ZXI6c2NhbGUtMTA1J1xuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZmlndXJlPlxuICAgICAgICAgICAgICA8ZmlndXJlIGNsYXNzTmFtZT0ndy1mdWxsJz5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9J2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDM0Mzg5Njc3NjY5LWUwOGI0Y2FjMzEwNT93PTUwMCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCdcbiAgICAgICAgICAgICAgICAgIGFsdD0nRmFzaGlvbiBoZXJpdGFnZSdcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHctZnVsbCBoLTk2IGFsaWduLWJvdHRvbSBvYmplY3QtY292ZXIgcm91bmRlZC1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdzdGlja3kgdG9wLTAgaC1zY3JlZW4gdy1mdWxsIGNvbC1zcGFuLTQgZ2FwLTMgZ3JpZCBncmlkLXJvd3MtMyc+XG4gICAgICAgICAgICAgIDxmaWd1cmUgY2xhc3NOYW1lPSd3LWZ1bGwgaC1mdWxsIHJlbGF0aXZlIGdyb3VwJz5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9J2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDQ1MjA1MTcwMjMwLTA1M2I4MzAxNjA1MD93PTUwMCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCdcbiAgICAgICAgICAgICAgICAgIGFsdD0nSk9PS0EgaGVyaXRhZ2UgY29sbGVjdGlvbidcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGgtZnVsbCB3LWZ1bGwgYWxpZ24tYm90dG9tIG9iamVjdC1jb3ZlciByb3VuZGVkLW1kIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdmlhLXRyYW5zcGFyZW50IHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtbWRcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTYgbGVmdC02IHJpZ2h0LTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VyaWYgZm9udC1saWdodCB0ZXh0LWdvbGQgbWItMlwiPjIwMTk8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWl2b3J5LzgwIGZvbnQtbGlnaHRcIj5UaGUgYmVnaW5uaW5nIG9mIG91ciBqb3VybmV5PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2ZpZ3VyZT5cbiAgICAgICAgICAgICAgPGZpZ3VyZSBjbGFzc05hbWU9J3ctZnVsbCBoLWZ1bGwgcmVsYXRpdmUgZ3JvdXAnPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz0naHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0OTA0ODE2NTE4NzEtYWI2OGRlMjVkNDNkP3c9NTAwJmF1dG89Zm9ybWF0JmZpdD1jcm9wJ1xuICAgICAgICAgICAgICAgICAgYWx0PSdUaW1lbGVzcyBlbGVnYW5jZSdcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGgtZnVsbCB3LWZ1bGwgYWxpZ24tYm90dG9tIG9iamVjdC1jb3ZlciByb3VuZGVkLW1kIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdmlhLXRyYW5zcGFyZW50IHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtbWRcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTYgbGVmdC02IHJpZ2h0LTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VyaWYgZm9udC1saWdodCB0ZXh0LWdvbGQgbWItMlwiPkNyYWZ0c21hbnNoaXA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWl2b3J5LzgwIGZvbnQtbGlnaHRcIj5NZXRpY3Vsb3VzIGF0dGVudGlvbiB0byBkZXRhaWw8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZmlndXJlPlxuICAgICAgICAgICAgICA8ZmlndXJlIGNsYXNzTmFtZT0ndy1mdWxsIGgtZnVsbCByZWxhdGl2ZSBncm91cCc+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPSdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ4Mzk4NTk4ODM1NS03NjM3MjhlMTkzNWI/dz01MDAmYXV0bz1mb3JtYXQmZml0PWNyb3AnXG4gICAgICAgICAgICAgICAgICBhbHQ9J01vZGVybiBsdXh1cnkgZmFzaGlvbidcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGgtZnVsbCB3LWZ1bGwgYWxpZ24tYm90dG9tIG9iamVjdC1jb3ZlciByb3VuZGVkLW1kIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdmlhLXRyYW5zcGFyZW50IHRvLXRyYW5zcGFyZW50IHJvdW5kZWQtbWRcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTYgbGVmdC02IHJpZ2h0LTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VyaWYgZm9udC1saWdodCB0ZXh0LWdvbGQgbWItMlwiPklubm92YXRpb248L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWl2b3J5LzgwIGZvbnQtbGlnaHRcIj5Nb2Rlcm4gbHV4dXJ5IHJlZGVmaW5lZDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdncmlkIGdhcC0zIGNvbC1zcGFuLTQnPlxuICAgICAgICAgICAgICA8ZmlndXJlIGNsYXNzTmFtZT0ndy1mdWxsJz5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9J2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk2NzQ3NjExMTc2LTg0MzIyMmUxZTU3Yz93PTUwMCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCdcbiAgICAgICAgICAgICAgICAgIGFsdD0nRmFzaGlvbiBpbm5vdmF0aW9uJ1xuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgdy1mdWxsIGgtOTYgYWxpZ24tYm90dG9tIG9iamVjdC1jb3ZlciByb3VuZGVkLW1kIGhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2ZpZ3VyZT5cbiAgICAgICAgICAgICAgPGZpZ3VyZSBjbGFzc05hbWU9J3ctZnVsbCc+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPSdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ0MTk4NDkwNDk5Ni1lMGI2YmE2ODdlMDQ/dz01MDAmYXV0bz1mb3JtYXQmZml0PWNyb3AnXG4gICAgICAgICAgICAgICAgICBhbHQ9J0x1eHVyeSByZXRhaWwgZXhwZXJpZW5jZSdcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHctZnVsbCBoLTk2IGFsaWduLWJvdHRvbSBvYmplY3QtY292ZXIgcm91bmRlZC1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICAgIDxmaWd1cmUgY2xhc3NOYW1lPSd3LWZ1bGwnPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz0naHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTg2MTg2NjYtZmNkMjVjODVjZDY0P3c9NTAwJmF1dG89Zm9ybWF0JmZpdD1jcm9wJ1xuICAgICAgICAgICAgICAgICAgYWx0PSdTdXN0YWluYWJsZSBsdXh1cnknXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J3RyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCB3LWZ1bGwgaC05NiBhbGlnbi1ib3R0b20gb2JqZWN0LWNvdmVyIHJvdW5kZWQtbWQgaG92ZXI6c2NhbGUtMTA1J1xuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZmlndXJlPlxuICAgICAgICAgICAgICA8ZmlndXJlIGNsYXNzTmFtZT0ndy1mdWxsJz5cbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9J2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTA5NjMxMTc5NjQ3LTAxNzczMzE2OTNhZT93PTUwMCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCdcbiAgICAgICAgICAgICAgICAgIGFsdD0nRmFzaGlvbiBjcmFmdHNtYW5zaGlwJ1xuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgdy1mdWxsIGgtOTYgYWxpZ24tYm90dG9tIG9iamVjdC1jb3ZlciByb3VuZGVkLW1kIGhvdmVyOnNjYWxlLTEwNSdcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2ZpZ3VyZT5cbiAgICAgICAgICAgICAgPGZpZ3VyZSBjbGFzc05hbWU9J3ctZnVsbCc+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPSdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ0MTk4NjMwMDkxNy02NDY3NGJkNjAwZDg/dz01MDAmYXV0bz1mb3JtYXQmZml0PWNyb3AnXG4gICAgICAgICAgICAgICAgICBhbHQ9J0pPT0tBIGxlZ2FjeSdcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIHctZnVsbCBoLTk2IGFsaWduLWJvdHRvbSBvYmplY3QtY292ZXIgcm91bmRlZC1tZCBob3ZlcjpzY2FsZS0xMDUnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9maWd1cmU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPSdncm91cCBiZy1ibGFjayBweS0yMCc+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtcHggYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS1nb2xkIHRvLXRyYW5zcGFyZW50IHctMzIgbXgtYXV0b1wiIC8+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtWzEydnddIG1kOnRleHQtWzh2d10gbGc6dGV4dC1bNnZ3XSBsZWFkaW5nLVsxMDAlXSB1cHBlcmNhc2UgZm9udC1zZXJpZiBmb250LWxpZ2h0IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1nb2xkIHZpYS1nb2xkLzgwIHRvLWl2b3J5IGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IHRyYW5zaXRpb24tYWxsIGVhc2UtbGluZWFyJz5cbiAgICAgICAgICAgICAgICBKT09LQVxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWl2b3J5LzYwIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIFdoZXJlIHRpbWVsZXNzIGVsZWdhbmNlIG1lZXRzIG1vZGVybiBjcmFmdHNtYW5zaGlwLiBFdmVyeSBwaWVjZSB0ZWxscyBhIHN0b3J5IG9mIGhlcml0YWdlLCBzdXN0YWluYWJpbGl0eSwgYW5kIHVuY29tcHJvbWlzaW5nIHF1YWxpdHkuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLXB4IGJnLWdyYWRpZW50LXRvLXIgZnJvbS10cmFuc3BhcmVudCB2aWEtZ29sZCB0by10cmFuc3BhcmVudCB3LTMyIG14LWF1dG9cIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZm9vdGVyPlxuICAgICAgPC9tYWluPlxuICAgIDwvUmVhY3RMZW5pcz5cbiAgKTtcbn0pO1xuXG5TdGlja3lTY3JvbGxDb21wb25lbnQuZGlzcGxheU5hbWUgPSAnU3RpY2t5U2Nyb2xsQ29tcG9uZW50JztcblxuZXhwb3J0IGRlZmF1bHQgU3RpY2t5U2Nyb2xsQ29tcG9uZW50O1xuIl0sIm5hbWVzIjpbIlJlYWN0TGVuaXMiLCJSZWFjdCIsImZvcndhcmRSZWYiLCJTdGlja3lTY3JvbGxDb21wb25lbnQiLCJwcm9wcyIsInJlZiIsInJvb3QiLCJtYWluIiwiY2xhc3NOYW1lIiwiZGl2Iiwic2VjdGlvbiIsInNwYW4iLCJoMSIsInAiLCJmaWd1cmUiLCJpbWciLCJzcmMiLCJhbHQiLCJoMyIsImZvb3RlciIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/sticky-scroll.tsx\n"));

/***/ })

});