"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lenis";
exports.ids = ["vendor-chunks/lenis"];
exports.modules = {

/***/ "(ssr)/./node_modules/lenis/dist/lenis-react.mjs":
/*!*************************************************!*\
  !*** ./node_modules/lenis/dist/lenis-react.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lenis: () => (/* binding */ ReactLenis),\n/* harmony export */   LenisContext: () => (/* binding */ LenisContext),\n/* harmony export */   ReactLenis: () => (/* binding */ ReactLenis),\n/* harmony export */   \"default\": () => (/* binding */ ReactLenis),\n/* harmony export */   useLenis: () => (/* binding */ useLenis)\n/* harmony export */ });\n/* harmony import */ var lenis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lenis */ \"(ssr)/./node_modules/lenis/dist/lenis.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Lenis,LenisContext,ReactLenis,default,useLenis auto */ // packages/react/src/provider.tsx\n\n\n// packages/react/src/store.ts\n\nvar Store = class {\n    constructor(state){\n        this.listeners = [];\n        this.state = state;\n    }\n    set(state) {\n        this.state = state;\n        for (let listener of this.listeners){\n            listener(this.state);\n        }\n    }\n    subscribe(listener) {\n        this.listeners = [\n            ...this.listeners,\n            listener\n        ];\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    get() {\n        return this.state;\n    }\n};\nfunction useStore(store) {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(store.get());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return store.subscribe((state2)=>setState(state2));\n    }, [\n        store\n    ]);\n    return state;\n}\n// packages/react/src/provider.tsx\n\nvar LenisContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nvar rootLenisContextStore = new Store(null);\nvar ReactLenis = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(({ children, root = false, options = {}, className, autoRaf = true, style, props }, ref)=>{\n    const wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [lenis, setLenis] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, ()=>({\n            wrapper: wrapperRef.current,\n            content: contentRef.current,\n            lenis\n        }), [\n        lenis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const lenis2 = new lenis__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            ...options,\n            ...!root && {\n                wrapper: wrapperRef.current,\n                content: contentRef.current\n            },\n            autoRaf: options?.autoRaf ?? autoRaf\n        });\n        setLenis(lenis2);\n        return ()=>{\n            lenis2.destroy();\n            setLenis(void 0);\n        };\n    }, [\n        root,\n        JSON.stringify(options)\n    ]);\n    const callbacksRefs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const addCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback, priority)=>{\n        callbacksRefs.current.push({\n            callback,\n            priority\n        });\n        callbacksRefs.current.sort((a, b)=>a.priority - b.priority);\n    }, []);\n    const removeCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n        callbacksRefs.current = callbacksRefs.current.filter((cb)=>cb.callback !== callback);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (root && lenis) {\n            rootLenisContextStore.set({\n                lenis,\n                addCallback,\n                removeCallback\n            });\n            return ()=>rootLenisContextStore.set(null);\n        }\n    }, [\n        root,\n        lenis,\n        addCallback,\n        removeCallback\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!lenis) return;\n        const onScroll = (data)=>{\n            for(let i = 0; i < callbacksRefs.current.length; i++){\n                callbacksRefs.current[i]?.callback(data);\n            }\n        };\n        lenis.on(\"scroll\", onScroll);\n        return ()=>{\n            lenis.off(\"scroll\", onScroll);\n        };\n    }, [\n        lenis\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LenisContext.Provider, {\n        value: {\n            lenis,\n            addCallback,\n            removeCallback\n        },\n        children: root ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n            ref: wrapperRef,\n            className,\n            style,\n            ...props,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                ref: contentRef,\n                children\n            })\n        })\n    });\n});\n// packages/react/src/use-lenis.ts\n\nvar fallbackContext = {};\nfunction useLenis(callback, deps = [], priority = 0) {\n    const localContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(LenisContext);\n    const rootContext = useStore(rootLenisContextStore);\n    const currentContext = localContext ?? rootContext ?? fallbackContext;\n    const { lenis, addCallback, removeCallback } = currentContext;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!callback || !addCallback || !removeCallback || !lenis) return;\n        addCallback(callback, priority);\n        callback(lenis);\n        return ()=>{\n            removeCallback(callback);\n        };\n    }, [\n        lenis,\n        addCallback,\n        removeCallback,\n        priority,\n        ...deps\n    ]);\n    return lenis;\n}\n //# sourceMappingURL=lenis-react.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lenis/dist/lenis-react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/lenis/dist/lenis.mjs":
/*!*******************************************!*\
  !*** ./node_modules/lenis/dist/lenis.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Lenis)\n/* harmony export */ });\n// package.json\nvar version = \"1.3.8\";\n\n// packages/core/src/maths.ts\nfunction clamp(min, input, max) {\n  return Math.max(min, Math.min(input, max));\n}\nfunction lerp(x, y, t) {\n  return (1 - t) * x + t * y;\n}\nfunction damp(x, y, lambda, deltaTime) {\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime));\n}\nfunction modulo(n, d) {\n  return (n % d + d) % d;\n}\n\n// packages/core/src/animate.ts\nvar Animate = class {\n  isRunning = false;\n  value = 0;\n  from = 0;\n  to = 0;\n  currentTime = 0;\n  // These are instanciated in the fromTo method\n  lerp;\n  duration;\n  easing;\n  onUpdate;\n  /**\n   * Advance the animation by the given delta time\n   *\n   * @param deltaTime - The time in seconds to advance the animation\n   */\n  advance(deltaTime) {\n    if (!this.isRunning) return;\n    let completed = false;\n    if (this.duration && this.easing) {\n      this.currentTime += deltaTime;\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1);\n      completed = linearProgress >= 1;\n      const easedProgress = completed ? 1 : this.easing(linearProgress);\n      this.value = this.from + (this.to - this.from) * easedProgress;\n    } else if (this.lerp) {\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime);\n      if (Math.round(this.value) === this.to) {\n        this.value = this.to;\n        completed = true;\n      }\n    } else {\n      this.value = this.to;\n      completed = true;\n    }\n    if (completed) {\n      this.stop();\n    }\n    this.onUpdate?.(this.value, completed);\n  }\n  /** Stop the animation */\n  stop() {\n    this.isRunning = false;\n  }\n  /**\n   * Set up the animation from a starting value to an ending value\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\n   *\n   * @param from - The starting value\n   * @param to - The ending value\n   * @param options - Options for the animation\n   */\n  fromTo(from, to, { lerp: lerp2, duration, easing, onStart, onUpdate }) {\n    this.from = this.value = from;\n    this.to = to;\n    this.lerp = lerp2;\n    this.duration = duration;\n    this.easing = easing;\n    this.currentTime = 0;\n    this.isRunning = true;\n    onStart?.();\n    this.onUpdate = onUpdate;\n  }\n};\n\n// packages/core/src/debounce.ts\nfunction debounce(callback, delay) {\n  let timer;\n  return function(...args) {\n    let context = this;\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      timer = void 0;\n      callback.apply(context, args);\n    }, delay);\n  };\n}\n\n// packages/core/src/dimensions.ts\nvar Dimensions = class {\n  constructor(wrapper, content, { autoResize = true, debounce: debounceValue = 250 } = {}) {\n    this.wrapper = wrapper;\n    this.content = content;\n    if (autoResize) {\n      this.debouncedResize = debounce(this.resize, debounceValue);\n      if (this.wrapper instanceof Window) {\n        window.addEventListener(\"resize\", this.debouncedResize, false);\n      } else {\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize);\n        this.wrapperResizeObserver.observe(this.wrapper);\n      }\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize);\n      this.contentResizeObserver.observe(this.content);\n    }\n    this.resize();\n  }\n  width = 0;\n  height = 0;\n  scrollHeight = 0;\n  scrollWidth = 0;\n  // These are instanciated in the constructor as they need information from the options\n  debouncedResize;\n  wrapperResizeObserver;\n  contentResizeObserver;\n  destroy() {\n    this.wrapperResizeObserver?.disconnect();\n    this.contentResizeObserver?.disconnect();\n    if (this.wrapper === window && this.debouncedResize) {\n      window.removeEventListener(\"resize\", this.debouncedResize, false);\n    }\n  }\n  resize = () => {\n    this.onWrapperResize();\n    this.onContentResize();\n  };\n  onWrapperResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.width = window.innerWidth;\n      this.height = window.innerHeight;\n    } else {\n      this.width = this.wrapper.clientWidth;\n      this.height = this.wrapper.clientHeight;\n    }\n  };\n  onContentResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.scrollHeight = this.content.scrollHeight;\n      this.scrollWidth = this.content.scrollWidth;\n    } else {\n      this.scrollHeight = this.wrapper.scrollHeight;\n      this.scrollWidth = this.wrapper.scrollWidth;\n    }\n  };\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height\n    };\n  }\n};\n\n// packages/core/src/emitter.ts\nvar Emitter = class {\n  events = {};\n  /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */\n  emit(event, ...args) {\n    let callbacks = this.events[event] || [];\n    for (let i = 0, length = callbacks.length; i < length; i++) {\n      callbacks[i]?.(...args);\n    }\n  }\n  /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */\n  on(event, cb) {\n    this.events[event]?.push(cb) || (this.events[event] = [cb]);\n    return () => {\n      this.events[event] = this.events[event]?.filter((i) => cb !== i);\n    };\n  }\n  /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event, callback) {\n    this.events[event] = this.events[event]?.filter((i) => callback !== i);\n  }\n  /**\n   * Remove all event listeners and clean up\n   */\n  destroy() {\n    this.events = {};\n  }\n};\n\n// packages/core/src/virtual-scroll.ts\nvar LINE_HEIGHT = 100 / 6;\nvar listenerOptions = { passive: false };\nvar VirtualScroll = class {\n  constructor(element, options = { wheelMultiplier: 1, touchMultiplier: 1 }) {\n    this.element = element;\n    this.options = options;\n    window.addEventListener(\"resize\", this.onWindowResize, false);\n    this.onWindowResize();\n    this.element.addEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.addEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.addEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.addEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n  }\n  touchStart = {\n    x: 0,\n    y: 0\n  };\n  lastDelta = {\n    x: 0,\n    y: 0\n  };\n  window = {\n    width: 0,\n    height: 0\n  };\n  emitter = new Emitter();\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  /** Remove all event listeners and clean up */\n  destroy() {\n    this.emitter.destroy();\n    window.removeEventListener(\"resize\", this.onWindowResize, false);\n    this.element.removeEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.removeEventListener(\n      \"touchstart\",\n      this.onTouchStart,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchmove\",\n      this.onTouchMove,\n      listenerOptions\n    );\n    this.element.removeEventListener(\n      \"touchend\",\n      this.onTouchEnd,\n      listenerOptions\n    );\n  }\n  /**\n   * Event handler for 'touchstart' event\n   *\n   * @param event Touch event\n   */\n  onTouchStart = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: 0,\n      y: 0\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX: 0,\n      deltaY: 0,\n      event\n    });\n  };\n  /** Event handler for 'touchmove' event */\n  onTouchMove = (event) => {\n    const { clientX, clientY } = event.targetTouches ? event.targetTouches[0] : event;\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier;\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: deltaX,\n      y: deltaY\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n  };\n  onTouchEnd = (event) => {\n    this.emitter.emit(\"scroll\", {\n      deltaX: this.lastDelta.x,\n      deltaY: this.lastDelta.y,\n      event\n    });\n  };\n  /** Event handler for 'wheel' event */\n  onWheel = (event) => {\n    let { deltaX, deltaY, deltaMode } = event;\n    const multiplierX = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1;\n    const multiplierY = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1;\n    deltaX *= multiplierX;\n    deltaY *= multiplierY;\n    deltaX *= this.options.wheelMultiplier;\n    deltaY *= this.options.wheelMultiplier;\n    this.emitter.emit(\"scroll\", { deltaX, deltaY, event });\n  };\n  onWindowResize = () => {\n    this.window = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  };\n};\n\n// packages/core/src/lenis.ts\nvar defaultEasing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t));\nvar Lenis = class {\n  _isScrolling = false;\n  // true when scroll is animating\n  _isStopped = false;\n  // true if user should not be able to scroll - enable/disable programmatically\n  _isLocked = false;\n  // same as isStopped but enabled/disabled when scroll reaches target\n  _preventNextNativeScrollEvent = false;\n  _resetVelocityTimeout = null;\n  __rafID = null;\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching;\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0;\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData = {};\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0;\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0;\n  /**\n   * The direction of the scroll\n   */\n  direction = 0;\n  /**\n   * The options passed to the lenis instance\n   */\n  options;\n  /**\n   * The target scroll value\n   */\n  targetScroll;\n  /**\n   * The animated scroll value\n   */\n  animatedScroll;\n  // These are instanciated here as they don't need information from the options\n  animate = new Animate();\n  emitter = new Emitter();\n  // These are instanciated in the constructor as they need information from the options\n  dimensions;\n  // This is not private because it's used in the Snap class\n  virtualScroll;\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration,\n    // in seconds\n    easing,\n    lerp: lerp2 = 0.1,\n    infinite = false,\n    orientation = \"vertical\",\n    // vertical, horizontal\n    gestureOrientation = \"vertical\",\n    // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false,\n    // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false\n  } = {}) {\n    window.lenisVersion = version;\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp: lerp2,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions\n    };\n    this.dimensions = new Dimensions(wrapper, content, { autoResize });\n    this.updateClassName();\n    this.targetScroll = this.animatedScroll = this.actualScroll;\n    this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, false);\n    this.options.wrapper.addEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.options.wrapper.addEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\n      touchMultiplier,\n      wheelMultiplier\n    });\n    this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener(\"transitionend\", this.onTransitionEnd, {\n        passive: true\n      });\n    }\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  }\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy();\n    this.options.wrapper.removeEventListener(\n      \"scroll\",\n      this.onNativeScroll,\n      false\n    );\n    this.options.wrapper.removeEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    this.options.wrapper.removeEventListener(\n      \"pointerdown\",\n      this.onPointerDown,\n      false\n    );\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        \"click\",\n        this.onClick,\n        false\n      );\n    }\n    this.virtualScroll.destroy();\n    this.dimensions.destroy();\n    this.cleanUpClassName();\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID);\n    }\n  }\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  off(event, callback) {\n    return this.emitter.off(event, callback);\n  }\n  onScrollEnd = (e) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === \"smooth\" || this.isScrolling === false) {\n        e.stopPropagation();\n      }\n    }\n  };\n  dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent(\"scrollend\", {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true\n        }\n      })\n    );\n  };\n  onTransitionEnd = (event) => {\n    if (event.propertyName.includes(\"overflow\")) {\n      const property = this.isHorizontal ? \"overflow-x\" : \"overflow-y\";\n      const overflow = getComputedStyle(this.rootElement)[property];\n      if ([\"hidden\", \"clip\"].includes(overflow)) {\n        this.internalStop();\n      } else {\n        this.internalStart();\n      }\n    }\n  };\n  setScroll(scroll) {\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: \"instant\" });\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: \"instant\" });\n    }\n  }\n  onClick = (event) => {\n    const path = event.composedPath();\n    const anchor = path.find(\n      (node) => node instanceof HTMLAnchorElement && (node.getAttribute(\"href\")?.startsWith(\"#\") || node.getAttribute(\"href\")?.startsWith(\"/#\") || node.getAttribute(\"href\")?.startsWith(\"./#\"))\n    );\n    if (anchor) {\n      const id = anchor.getAttribute(\"href\");\n      if (id) {\n        const options = typeof this.options.anchors === \"object\" && this.options.anchors ? this.options.anchors : void 0;\n        let target = `#${id.split(\"#\")[1]}`;\n        if ([\"#\", \"/#\", \"./#\", \"#top\", \"/#top\", \"./#top\"].includes(id)) {\n          target = 0;\n        }\n        this.scrollTo(target, options);\n      }\n    }\n  };\n  onPointerDown = (event) => {\n    if (event.button === 1) {\n      this.reset();\n    }\n  };\n  onVirtualScroll = (data) => {\n    if (typeof this.options.virtualScroll === \"function\" && this.options.virtualScroll(data) === false)\n      return;\n    const { deltaX, deltaY, event } = data;\n    this.emitter.emit(\"virtual-scroll\", { deltaX, deltaY, event });\n    if (event.ctrlKey) return;\n    if (event.lenisStopPropagation) return;\n    const isTouch = event.type.includes(\"touch\");\n    const isWheel = event.type.includes(\"wheel\");\n    this.isTouching = event.type === \"touchstart\" || event.type === \"touchmove\";\n    const isClickOrTap = deltaX === 0 && deltaY === 0;\n    const isTapToStop = this.options.syncTouch && isTouch && event.type === \"touchstart\" && isClickOrTap && !this.isStopped && !this.isLocked;\n    if (isTapToStop) {\n      this.reset();\n      return;\n    }\n    const isUnknownGesture = this.options.gestureOrientation === \"vertical\" && deltaY === 0 || this.options.gestureOrientation === \"horizontal\" && deltaX === 0;\n    if (isClickOrTap || isUnknownGesture) {\n      return;\n    }\n    let composedPath = event.composedPath();\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement));\n    const prevent = this.options.prevent;\n    if (!!composedPath.find(\n      (node) => node instanceof HTMLElement && (typeof prevent === \"function\" && prevent?.(node) || node.hasAttribute?.(\"data-lenis-prevent\") || isTouch && node.hasAttribute?.(\"data-lenis-prevent-touch\") || isWheel && node.hasAttribute?.(\"data-lenis-prevent-wheel\") || this.options.allowNestedScroll && this.checkNestedScroll(node, { deltaX, deltaY }))\n    ))\n      return;\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    const isSmooth = this.options.syncTouch && isTouch || this.options.smoothWheel && isWheel;\n    if (!isSmooth) {\n      this.isScrolling = \"native\";\n      this.animate.stop();\n      event.lenisStopPropagation = true;\n      return;\n    }\n    let delta = deltaY;\n    if (this.options.gestureOrientation === \"both\") {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX;\n    } else if (this.options.gestureOrientation === \"horizontal\") {\n      delta = deltaX;\n    }\n    if (!this.options.overscroll || this.options.infinite || this.options.wrapper !== window && (this.animatedScroll > 0 && this.animatedScroll < this.limit || this.animatedScroll === 0 && deltaY > 0 || this.animatedScroll === this.limit && deltaY < 0)) {\n      event.lenisStopPropagation = true;\n    }\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    const isSyncTouch = isTouch && this.options.syncTouch;\n    const isTouchEnd = isTouch && event.type === \"touchend\";\n    const hasTouchInertia = isTouchEnd;\n    if (hasTouchInertia) {\n      delta = Math.sign(this.velocity) * Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent);\n    }\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...isSyncTouch ? {\n        lerp: hasTouchInertia ? this.options.syncTouchLerp : 1\n        // immediate: !hasTouchInertia,\n      } : {\n        lerp: this.options.lerp,\n        duration: this.options.duration,\n        easing: this.options.easing\n      }\n    });\n  };\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize();\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.emit();\n  }\n  emit() {\n    this.emitter.emit(\"scroll\", this);\n  }\n  onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout);\n      this._resetVelocityTimeout = null;\n    }\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false;\n      return;\n    }\n    if (this.isScrolling === false || this.isScrolling === \"native\") {\n      const lastScroll = this.animatedScroll;\n      this.animatedScroll = this.targetScroll = this.actualScroll;\n      this.lastVelocity = this.velocity;\n      this.velocity = this.animatedScroll - lastScroll;\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      );\n      if (!this.isStopped) {\n        this.isScrolling = \"native\";\n      }\n      this.emit();\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity;\n          this.velocity = 0;\n          this.isScrolling = false;\n          this.emit();\n        }, 400);\n      }\n    }\n  };\n  reset() {\n    this.isLocked = false;\n    this.isScrolling = false;\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.lastVelocity = this.velocity = 0;\n    this.animate.stop();\n  }\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty(\"overflow\");\n      return;\n    }\n    this.internalStart();\n  }\n  internalStart() {\n    if (!this.isStopped) return;\n    this.reset();\n    this.isStopped = false;\n    this.emit();\n  }\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty(\"overflow\", \"clip\");\n      return;\n    }\n    this.internalStop();\n  }\n  internalStop() {\n    if (this.isStopped) return;\n    this.reset();\n    this.isStopped = true;\n    this.emit();\n  }\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time) => {\n    const deltaTime = time - (this.time || time);\n    this.time = time;\n    this.animate.advance(deltaTime * 1e-3);\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  };\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(target, {\n    offset = 0,\n    immediate = false,\n    lock = false,\n    duration = this.options.duration,\n    easing = this.options.easing,\n    lerp: lerp2 = this.options.lerp,\n    onStart,\n    onComplete,\n    force = false,\n    // scroll even if stopped\n    programmatic = true,\n    // called from outside of the class\n    userData\n  } = {}) {\n    if ((this.isStopped || this.isLocked) && !force) return;\n    if (typeof target === \"string\" && [\"top\", \"left\", \"start\"].includes(target)) {\n      target = 0;\n    } else if (typeof target === \"string\" && [\"bottom\", \"right\", \"end\"].includes(target)) {\n      target = this.limit;\n    } else {\n      let node;\n      if (typeof target === \"string\") {\n        node = document.querySelector(target);\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        node = target;\n      }\n      if (node) {\n        if (this.options.wrapper !== window) {\n          const wrapperRect = this.rootElement.getBoundingClientRect();\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top;\n        }\n        const rect = node.getBoundingClientRect();\n        target = (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll;\n      }\n    }\n    if (typeof target !== \"number\") return;\n    target += offset;\n    target = Math.round(target);\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll;\n        const distance = target - this.animatedScroll;\n        if (distance > this.limit / 2) {\n          target = target - this.limit;\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit;\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit);\n    }\n    if (target === this.targetScroll) {\n      onStart?.(this);\n      onComplete?.(this);\n      return;\n    }\n    this.userData = userData ?? {};\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target;\n      this.setScroll(this.scroll);\n      this.reset();\n      this.preventNextNativeScrollEvent();\n      this.emit();\n      onComplete?.(this);\n      this.userData = {};\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent();\n      });\n      return;\n    }\n    if (!programmatic) {\n      this.targetScroll = target;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp: lerp2,\n      onStart: () => {\n        if (lock) this.isLocked = true;\n        this.isScrolling = \"smooth\";\n        onStart?.(this);\n      },\n      onUpdate: (value, completed) => {\n        this.isScrolling = \"smooth\";\n        this.lastVelocity = this.velocity;\n        this.velocity = value - this.animatedScroll;\n        this.direction = Math.sign(this.velocity);\n        this.animatedScroll = value;\n        this.setScroll(this.scroll);\n        if (programmatic) {\n          this.targetScroll = value;\n        }\n        if (!completed) this.emit();\n        if (completed) {\n          this.reset();\n          this.emit();\n          onComplete?.(this);\n          this.userData = {};\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent();\n          });\n          this.preventNextNativeScrollEvent();\n        }\n      }\n    });\n  }\n  preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true;\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false;\n    });\n  }\n  checkNestedScroll(node, { deltaX, deltaY }) {\n    const time = Date.now();\n    const cache = node._lenis ??= {};\n    let hasOverflowX, hasOverflowY, isScrollableX, isScrollableY, scrollWidth, scrollHeight, clientWidth, clientHeight;\n    const gestureOrientation = this.options.gestureOrientation;\n    if (time - (cache.time ?? 0) > 2e3) {\n      cache.time = Date.now();\n      const computedStyle = window.getComputedStyle(node);\n      cache.computedStyle = computedStyle;\n      const overflowXString = computedStyle.overflowX;\n      const overflowYString = computedStyle.overflowY;\n      hasOverflowX = [\"auto\", \"overlay\", \"scroll\"].includes(overflowXString);\n      hasOverflowY = [\"auto\", \"overlay\", \"scroll\"].includes(overflowYString);\n      cache.hasOverflowX = hasOverflowX;\n      cache.hasOverflowY = hasOverflowY;\n      if (!hasOverflowX && !hasOverflowY) return false;\n      if (gestureOrientation === \"vertical\" && !hasOverflowY) return false;\n      if (gestureOrientation === \"horizontal\" && !hasOverflowX) return false;\n      scrollWidth = node.scrollWidth;\n      scrollHeight = node.scrollHeight;\n      clientWidth = node.clientWidth;\n      clientHeight = node.clientHeight;\n      isScrollableX = scrollWidth > clientWidth;\n      isScrollableY = scrollHeight > clientHeight;\n      cache.isScrollableX = isScrollableX;\n      cache.isScrollableY = isScrollableY;\n      cache.scrollWidth = scrollWidth;\n      cache.scrollHeight = scrollHeight;\n      cache.clientWidth = clientWidth;\n      cache.clientHeight = clientHeight;\n    } else {\n      isScrollableX = cache.isScrollableX;\n      isScrollableY = cache.isScrollableY;\n      hasOverflowX = cache.hasOverflowX;\n      hasOverflowY = cache.hasOverflowY;\n      scrollWidth = cache.scrollWidth;\n      scrollHeight = cache.scrollHeight;\n      clientWidth = cache.clientWidth;\n      clientHeight = cache.clientHeight;\n    }\n    if (!hasOverflowX && !hasOverflowY || !isScrollableX && !isScrollableY) {\n      return false;\n    }\n    if (gestureOrientation === \"vertical\" && (!hasOverflowY || !isScrollableY))\n      return false;\n    if (gestureOrientation === \"horizontal\" && (!hasOverflowX || !isScrollableX))\n      return false;\n    let orientation;\n    if (gestureOrientation === \"horizontal\") {\n      orientation = \"x\";\n    } else if (gestureOrientation === \"vertical\") {\n      orientation = \"y\";\n    } else {\n      const isScrollingX = deltaX !== 0;\n      const isScrollingY = deltaY !== 0;\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = \"x\";\n      }\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = \"y\";\n      }\n    }\n    if (!orientation) return false;\n    let scroll, maxScroll, delta, hasOverflow, isScrollable;\n    if (orientation === \"x\") {\n      scroll = node.scrollLeft;\n      maxScroll = scrollWidth - clientWidth;\n      delta = deltaX;\n      hasOverflow = hasOverflowX;\n      isScrollable = isScrollableX;\n    } else if (orientation === \"y\") {\n      scroll = node.scrollTop;\n      maxScroll = scrollHeight - clientHeight;\n      delta = deltaY;\n      hasOverflow = hasOverflowY;\n      isScrollable = isScrollableY;\n    } else {\n      return false;\n    }\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0;\n    return willScroll && hasOverflow && isScrollable;\n  }\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n  }\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth;\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight;\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n    }\n  }\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === \"horizontal\";\n  }\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    const wrapper = this.options.wrapper;\n    return this.isHorizontal ? wrapper.scrollX ?? wrapper.scrollLeft : wrapper.scrollY ?? wrapper.scrollTop;\n  }\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite ? modulo(this.animatedScroll, this.limit) : this.animatedScroll;\n  }\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    return this.limit === 0 ? 1 : this.scroll / this.limit;\n  }\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling;\n  }\n  set isScrolling(value) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped;\n  }\n  set isStopped(value) {\n    if (this._isStopped !== value) {\n      this._isStopped = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked;\n  }\n  set isLocked(value) {\n    if (this._isLocked !== value) {\n      this._isLocked = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === \"smooth\";\n  }\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = \"lenis\";\n    if (this.options.autoToggle) className += \" lenis-autoToggle\";\n    if (this.isStopped) className += \" lenis-stopped\";\n    if (this.isLocked) className += \" lenis-locked\";\n    if (this.isScrolling) className += \" lenis-scrolling\";\n    if (this.isScrolling === \"smooth\") className += \" lenis-smooth\";\n    return className;\n  }\n  updateClassName() {\n    this.cleanUpClassName();\n    this.rootElement.className = `${this.rootElement.className} ${this.className}`.trim();\n  }\n  cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className.replace(/lenis(-\\w+)?/g, \"\").trim();\n  }\n};\n\n//# sourceMappingURL=lenis.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lenis/dist/lenis.mjs\n");

/***/ })

};
;